import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_button.dart';
import '../../core/widgets/app_text_field.dart';
// import 'driver_register_screen.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;

  const OtpVerificationScreen({
    super.key,
    required this.phoneNumber,
  });

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final List<TextEditingController> _otpControllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  Timer? _timer;
  int _resendTimer = 30;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  void _startResendTimer() {
    _canResend = false;
    _resendTimer = 30;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_resendTimer > 0) {
          _resendTimer--;
        } else {
          _canResend = true;
          timer.cancel();
        }
      });
    });
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 5) {
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  void _verifyOtp() {
    final otp = _otpControllers.map((c) => c.text).join();

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter all 6 digits'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => DriverRegisterScreen(
          phoneNumber: widget.phoneNumber,
        ),
      ),
    );
  }

  void _resendOtp() {
    if (_canResend) {
      for (var controller in _otpControllers) {
        controller.clear();
      }
      _focusNodes[0].requestFocus();
      _startResendTimer();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('OTP Resent Successfully!'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 40),

                /// Logo
                Image.asset('assets/images/logo.png', height: 80, width: 80),
                const SizedBox(height: 12),

                Text("NIKKOU",
                    style: AppTypography.h2.copyWith(
                        color: AppColors.textOnPrimary,
                        letterSpacing: 2,
                        fontWeight: FontWeight.bold)),
                Text("LOGISTICS PVT LTD",
                    style: AppTypography.label.copyWith(
                        color: AppColors.textOnPrimary,
                        letterSpacing: 1)),
                const SizedBox(height: 30),

                /// Truck
                Image.asset('assets/images/truck.png', height: 100, width: 160),

                const SizedBox(height: 40),

                /// Title
                Text("Verify OTP",
                    style: AppTypography.h1.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center),
                const SizedBox(height: 16),

                /// Instruction
                Text(
                  "Enter the 6-digit code\nsent to +91 ${widget.phoneNumber}",
                  style: AppTypography.body1
                      .copyWith(color: AppColors.textOnPrimary),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                /// OTP Fields
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(6, (index) {
                    return SizedBox(
                      width: 50,
                      height: 55,
                      child: AppTextField(
                        controller: _otpControllers[index],
                        focusNode: _focusNodes[index],
                        hintText: "-",
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.number,
                        borderRadius: 10,
                        fontSize: 20,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(1),
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onChanged: (value) => _onOtpChanged(value, index),
                      ),
                    );
                  }),
                ),

                const SizedBox(height: 30),

                /// Resend OTP
                GestureDetector(
                  onTap: _canResend ? _resendOtp : null,
                  child: Text(
                    _canResend
                        ? "Didn't receive? Resend OTP"
                        : "Resend in $_resendTimer s",
                    style: AppTypography.body2.copyWith(
                      color: _canResend
                          ? AppColors.secondary
                          : AppColors.textTertiary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),

                const SizedBox(height: 40),

                /// Verify Button
                AppButton(
                  label: "Verify & Continue",
                  isEnabled: true,
                  onPressed: _verifyOtp,
                  width: double.infinity,
                  height: 50,
                  borderRadius: 25,
                ),

                const SizedBox(height: 60),

                /// Footer Links
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text("Privacy Policy",
                        style: AppTypography.label.copyWith(
                          color: AppColors.textOnPrimary,
                          decoration: TextDecoration.underline,
                        )),
                    const SizedBox(width: 6),
                    Text("•", style: AppTypography.label.copyWith(color: AppColors.textOnPrimary)),
                    const SizedBox(width: 6),
                    Text("Terms",
                        style: AppTypography.label.copyWith(
                          color: AppColors.textOnPrimary,
                          decoration: TextDecoration.underline,
                        )),
                  ],
                ),
                const SizedBox(height: 8),

                Text("Having trouble? Contact support",
                    style: AppTypography.label.copyWith(
                      color: AppColors.textOnPrimary,
                      decoration: TextDecoration.underline,
                    )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
