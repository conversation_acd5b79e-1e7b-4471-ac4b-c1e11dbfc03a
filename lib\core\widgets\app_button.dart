// App button widget will be defined here
import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isEnabled;

  // Customizable properties
  final double? width;
  final double height;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry padding;
  final Gradient? gradient;
  final BoxDecoration? decoration;
  final TextStyle? textStyle;

  const AppButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.isEnabled = true,
    this.width,
    this.height = 50,
    this.borderRadius = 25,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 16),
    this.gradient,
    this.decoration,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width ?? double.infinity,
      height: height,
      decoration:
          decoration ??
          BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: isEnabled
                ? (gradient ??
                      const LinearGradient(
                        colors: [
                          AppColors.secondary,
                          Color.fromARGB(255, 226, 94, 6),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ))
                : null,
            color: !isEnabled ? AppColors.disabled : null,
          ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        onPressed: isEnabled ? onPressed : null,
        child: Padding(
          padding: padding,
          child: Text(label, style: textStyle ?? AppTypography.button),
        ),
      ),
    );
  }
}
